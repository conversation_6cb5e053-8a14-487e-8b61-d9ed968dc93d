import{_ as s,q as a,H as o,u as e,c as t,w as l,i as d,o as r,g as i,h as n,t as c,k as f,U as m,y as u}from"./index-x-DlrgMM.js";import{P as w}from"./PageHeader.BgJCaU6i.js";import{c as P}from"./sysuser.COxbtp6y.js";import{c as p,C as h}from"./adminAuthService.BHB8Py5D.js";const _=s({components:{PageHeader:w},data:()=>({form:{oldPassword:"",newPassword:"",confirmPassword:""},loading:!1}),methods:{validatePassword:s=>!s||s.length<6?"密码长度不能少于6位":/(?=.*[a-zA-Z])(?=.*\d)/.test(s)?null:"密码必须包含字母和数字",async changePassword(){if(!this.form.oldPassword)return void a({title:"请输入当前密码",icon:"none"});if(!this.form.newPassword)return void a({title:"请输入新密码",icon:"none"});if(this.form.newPassword!==this.form.confirmPassword)return void a({title:"两次输入的密码不一致",icon:"none"});const s=this.validatePassword(this.form.newPassword);if(s)a({title:s,icon:"none"});else if(this.form.oldPassword!==this.form.newPassword)try{this.loading=!0;const s=p();if(!s||!s.userId)return void a({title:"用户信息获取失败",icon:"none"});const e=h.MD5(this.form.oldPassword).toString(),t=h.MD5(this.form.newPassword).toString();await P({userId:s.userId,oldPassword:e,newPassword:t}),a({title:"密码修改成功",icon:"success"}),this.form={oldPassword:"",newPassword:"",confirmPassword:""},setTimeout((()=>{o()}),1500)}catch(e){a({title:e.message||"密码修改失败",icon:"none"})}finally{this.loading=!1}else a({title:"新密码不能与当前密码相同",icon:"none"})}}},[["render",function(s,a,o,w,P,p){const h=e("PageHeader"),_=f,g=m,v=d,V=u;return r(),t(v,{class:"container"},{default:l((()=>[i(h,{title:"修改密码"}),i(v,{class:"form-container"},{default:l((()=>[i(v,{class:"form-section"},{default:l((()=>[i(v,{class:"form-item"},{default:l((()=>[i(_,{class:"label"},{default:l((()=>[n("当前密码")])),_:1}),i(g,{type:"password",modelValue:P.form.oldPassword,"onUpdate:modelValue":a[0]||(a[0]=s=>P.form.oldPassword=s),placeholder:"请输入当前密码",class:"input"},null,8,["modelValue"])])),_:1}),i(v,{class:"form-item"},{default:l((()=>[i(_,{class:"label"},{default:l((()=>[n("新密码")])),_:1}),i(g,{type:"password",modelValue:P.form.newPassword,"onUpdate:modelValue":a[1]||(a[1]=s=>P.form.newPassword=s),placeholder:"请输入新密码",class:"input"},null,8,["modelValue"])])),_:1}),i(v,{class:"form-item"},{default:l((()=>[i(_,{class:"label"},{default:l((()=>[n("确认新密码")])),_:1}),i(g,{type:"password",modelValue:P.form.confirmPassword,"onUpdate:modelValue":a[2]||(a[2]=s=>P.form.confirmPassword=s),placeholder:"请再次输入新密码",class:"input"},null,8,["modelValue"])])),_:1}),i(v,{class:"password-tips"},{default:l((()=>[i(_,{class:"tip-title"},{default:l((()=>[n("密码要求：")])),_:1}),i(_,{class:"tip-item"},{default:l((()=>[n("• 长度不少于6位")])),_:1}),i(_,{class:"tip-item"},{default:l((()=>[n("• 必须包含字母和数字")])),_:1}),i(_,{class:"tip-item"},{default:l((()=>[n("• 建议包含特殊字符")])),_:1})])),_:1})])),_:1}),i(v,{class:"button-section"},{default:l((()=>[i(V,{class:"submit-btn",onClick:p.changePassword,disabled:P.loading},{default:l((()=>[n(c(P.loading?"修改中...":"确认修改"),1)])),_:1},8,["onClick","disabled"])])),_:1})])),_:1})])),_:1})}],["__scopeId","data-v-001c8e76"]]);export{_ as default};
