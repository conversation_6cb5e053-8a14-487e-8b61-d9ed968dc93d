import{_ as e,C as a,c as s,w as o,i as l,o as n,g as t,h as d,k as r}from"./index-x-DlrgMM.js";const m=e({name:"EmployeeListRedirect",onLoad(e){console.log("employee-list.vue 重定向到统一页面，参数:",e);let s="/pages/admin/users/user-management?type=employee";e.managerId&&(s+=`&managerId=${e.managerId}`),console.log("重定向URL:",s),a({url:s})}},[["render",function(e,a,m,c,u,g){const i=r,p=l;return n(),s(p,{class:"redirect-page"},{default:o((()=>[t(p,{class:"loading"},{default:o((()=>[t(i,null,{default:o((()=>[d("正在跳转到员工页面...")])),_:1})])),_:1})])),_:1})}],["__scopeId","data-v-f99dfb51"]]);export{m as default};
