import{_ as t,a6 as e,a9 as i,o as a,c as s,w as o,g as n,n as l,j as c,h as r,t as d,a7 as u,J as h,L as m,k as p,i as g,r as f,d as b,a as v,Q as y,v as T,F as S,x as w,af as x,ag as C,u as k,e as _,Z as B,q as P,N as D,V as L}from"./index-x-DlrgMM.js";import{q as I}from"./video.D-bDcJnf.js";import{m as M}from"./media-common.75pKBEhU.js";import{q as z}from"./batch.B7qFZ3Nu.js";import"./adminAuthService.BHB8Py5D.js";import"./format.t5pgP9mx.js";const $=t({name:"FloatingActionButton",props:{type:{type:String,default:"primary"},text:{type:String,default:""},icon:{type:String,default:""},iconClass:{type:String,default:""},initialPosition:{type:Object,default:()=>({right:20,bottom:120})}},data:()=>({btnPosition:{left:0,top:0},startTouch:{x:0,y:0}}),mounted(){this.initPosition()},methods:{initPosition(){e({success:t=>{void 0!==this.initialPosition.right?this.btnPosition.left=t.windowWidth-this.initialPosition.right-40:void 0!==this.initialPosition.left?this.btnPosition.left=this.initialPosition.left:this.btnPosition.left=t.windowWidth-60,void 0!==this.initialPosition.bottom?this.btnPosition.top=t.windowHeight-this.initialPosition.bottom-40:void 0!==this.initialPosition.top?this.btnPosition.top=this.initialPosition.top:this.btnPosition.top=t.windowHeight-160}})},touchStart(t){this.startTouch={x:t.touches[0].clientX,y:t.touches[0].clientY}},moveFloatingBtn(t){const e=t.touches[0];this.btnPosition.left=e.clientX-20,this.btnPosition.top=e.clientY-20},snapToEdge(){const t=i().windowWidth,e=i().windowHeight;this.btnPosition.left>t/2?this.btnPosition.left=t-40-15:this.btnPosition.left=15,this.btnPosition.top<100?this.btnPosition.top=100:this.btnPosition.top>e-150&&(this.btnPosition.top=e-150)},handleClick(){this.$emit("click")}}},[["render",function(t,e,i,f,b,v){const y=m,T=p,S=g;return a(),s(S,{class:"floating-btn-wrapper",style:u({left:b.btnPosition.left+"px",top:b.btnPosition.top+"px"}),onTouchstart:v.touchStart,onTouchmove:h(v.moveFloatingBtn,["stop","prevent"]),onTouchend:v.snapToEdge},{default:o((()=>[n(S,{class:l(["floating-action-btn",[`btn-${i.type}`]]),onClick:v.handleClick},{default:o((()=>[i.icon?(a(),s(y,{key:0,src:i.icon,mode:"aspectFit",class:"btn-icon"},null,8,["src"])):c("",!0),i.iconClass?(a(),s(T,{key:1,class:l(["iconfont",i.iconClass])},null,8,["class"])):c("",!0),i.text?(a(),s(T,{key:2,class:"btn-text"},{default:o((()=>[r(d(i.text),1)])),_:1})):c("",!0)])),_:1},8,["class","onClick"])])),_:1},8,["style","onTouchstart","onTouchmove","onTouchend"])}],["__scopeId","data-v-01aef420"]]);const U=t({name:"MediaCard",props:{item:{type:Object,required:!0,default:()=>({id:"",title:"",description:"",thumbnail:"",duration:"",statusText:"",statusType:"info",metaItems:[]})}},methods:{handleClick(t){t&&(t.stopPropagation(),t.preventDefault()),this.$emit("click",this.item)}}},[["render",function(t,e,i,l,u,h){const x=m,C=f(v("u-icon"),b),k=p,_=g,B=f(v("u-tag"),y);return a(),s(_,{class:"media-card",onClick:h.handleClick},{default:o((()=>[n(_,{class:"media-card-content"},{default:o((()=>[n(_,{class:"media-thumbnail"},{default:o((()=>[i.item.thumbnail?(a(),s(x,{key:0,src:i.item.thumbnail,mode:"aspectFill",class:"media-thumbnail-image"},null,8,["src"])):(a(),s(_,{key:1,class:"no-cover-placeholder"},{default:o((()=>[n(C,{name:"photo",size:"32",color:"#ccc"}),n(k,{style:{"font-size":"20rpx",color:"#999","margin-top":"8rpx"}},{default:o((()=>[r("无封面")])),_:1})])),_:1})),i.item.duration?(a(),s(k,{key:2,class:"media-duration"},{default:o((()=>[r(d(i.item.duration),1)])),_:1})):c("",!0),n(B,{text:i.item.statusText,type:i.item.statusType,size:"mini",class:"media-status"},null,8,["text","type"])])),_:1}),n(_,{class:"media-info"},{default:o((()=>[n(k,{class:"media-title"},{default:o((()=>[r(d(i.item.title),1)])),_:1}),i.item.description?(a(),s(k,{key:0,class:"media-description"},{default:o((()=>[r(d(i.item.description),1)])),_:1})):c("",!0),n(_,{class:"media-meta"},{default:o((()=>[(a(!0),T(S,null,w(i.item.metaItems,((t,e)=>(a(),s(_,{class:"meta-item",key:e},{default:o((()=>[n(k,{class:"meta-icon"},{default:o((()=>[r(d(t.icon),1)])),_:2},1024),n(k,null,{default:o((()=>[r(d(t.text),1)])),_:2},1024)])),_:2},1024)))),128))])),_:1})])),_:1})])),_:1})])),_:1},8,["onClick"])}],["__scopeId","data-v-137b0bf3"]]);const F=t({components:{VideosList:t({mixins:[M],components:{FloatingActionButton:$,MediaCard:U},data:()=>({mediaList:[],currentStatus:"all"}),computed:{filteredMedia(){let t=this.mediaList;return"all"!==this.currentStatus&&(t=t.filter((t=>t.status===this.currentStatus))),t}},created(){this.loadAllMedia(),x("refreshVideoList",(()=>{this.loadAllMedia()}))},beforeDestroy(){C("refreshVideoList")},methods:{async loadAllMedia(){try{this.showLoading("加载中...");const t=await I({page:1,pageSize:1e3});t.success&&t.data?this.mediaList=t.data.items.map((t=>({id:t.id,title:t.title,description:t.description,thumbnail:this.buildCompleteFileUrl(t.coverUrl)||"/assets/images/video-cover.jpg",duration:this.formatDuration(t.duration),uploader:t.creatorName||t.createdBy||"管理员",uploadTime:t.createTime,status:t.status,videoUrl:this.buildCompleteFileUrl(t.videoUrl),fileSize:t.fileSize,views:t.views||0,likes:t.likes||0,comments:t.comments||0}))):(console.error("获取视频列表失败:",t.message),this.showError(t.message||"获取视频列表失败"),this.mediaList=[]),this.hideLoading()}catch(t){console.error("加载视频列表失败:",t),this.hideLoading(),this.showError("加载失败"),this.mediaList=[]}},showUploadModal(){this.safeNavigateTo("/pages/admin/media/upload")},viewMediaDetail(t){const e=t.originalData||t;e&&e.id?this.safeNavigateTo(`/pages/admin/media/detail?id=${e.id}`):this.showError("视频ID无效")},getVideoStatusText:t=>({0:"已下架",1:"已上架",2:"处理失败",3:"压缩中"}[t.status]||"未知状态"),getVideoStatusType:t=>({0:"error",1:"success",2:"error",3:"warning"}[t.status]||"info"),getStatusLabel:t=>({all:"",0:"已下架",1:"已上架",2:"处理失败",3:"压缩中"}[t]||""),formatFileSize(t){if(!t)return"";const e=Math.floor(Math.log(t)/Math.log(1024));return Math.round(t/Math.pow(1024,e)*100)/100+" "+["B","KB","MB","GB"][e]},formatVideoItem(t){const e=[{icon:"📅",text:this.formatDate(t.uploadTime)},{icon:"👤",text:t.uploader}];return t.fileSize&&e.push({icon:"📁",text:this.formatFileSize(t.fileSize)}),{id:t.id,title:t.title,description:t.description,thumbnail:t.thumbnail,duration:t.duration,statusText:this.getVideoStatusText(t),statusType:this.getVideoStatusType(t),metaItems:e,originalData:t}}}},[["render",function(t,e,i,l,r,d){const u=k("MediaCard"),h=f(v("u-button"),_),m=f(v("u-empty"),B),p=g,b=k("FloatingActionButton");return a(),s(p,{class:"container"},{default:o((()=>[n(p,{class:"media-list-container"},{default:o((()=>[(a(!0),T(S,null,w(d.filteredMedia,((t,e)=>(a(),s(u,{key:e,item:d.formatVideoItem(t),onClick:d.viewMediaDetail},null,8,["item","onClick"])))),128)),0===d.filteredMedia.length?(a(),s(m,{key:0,mode:"list",text:`暂无${d.getStatusLabel(r.currentStatus)}视频`,iconSize:"120",textSize:"16",marginTop:"100"},{default:o((()=>[n(h,{type:"primary",text:"上传视频",onClick:d.showUploadModal,size:"normal",shape:"round"},null,8,["onClick"])])),_:1},8,["text"])):c("",!0)])),_:1}),n(b,{text:"上传",type:"primary",initialPosition:{right:20,bottom:180},onClick:d.showUploadModal},null,8,["onClick"])])),_:1})}],["__scopeId","data-v-daa91491"]]),BatchesList:t({mixins:[M],components:{MediaCard:U},data:()=>({batchList:[],batchStatusTabs:[{name:"全部",value:"all"},{name:"进行中",value:"active"},{name:"未开始",value:"pending"},{name:"已结束",value:"ended"}],currentBatchStatus:"all",currentTabIndex:0}),computed:{filteredBatches(){let t=this.batchList;return"all"!==this.currentBatchStatus&&(t=t.filter((t=>t.status===this.currentBatchStatus))),t}},created(){this.loadAllBatches()},methods:{async loadAllBatches(){try{this.showLoading("加载批次中...");const t=await z({page:1,pageSize:1e3});t.success&&t.data?this.batchList=t.data.items.map((t=>{console.log("批次数据:",t),console.log("videoCoverUrl:",t.videoCoverUrl);const e=this.buildCompleteFileUrl(t.videoCoverUrl);return console.log("处理后的封面URL:",e),{id:t.id,batchId:t.batchId,title:t.name||t.title,description:t.description,startTime:t.startTime,endTime:t.endTime,status:this.calculateBatchStatus(t),participants:t.participants||0,totalReward:t.totalReward||0,videos:t.videoId?[{id:t.videoId,title:t.videoTitle,cover:e,duration:t.videoDuration}]:[],createdAt:t.createdAt}})):(console.error("获取批次列表失败:",t.message),this.showError(t.message||"获取批次列表失败"),this.batchList=[]),this.hideLoading()}catch(t){console.error("加载批次列表失败:",t),this.hideLoading(),this.showError("加载失败"),this.batchList=[]}},calculateBatchStatus(t){const e=new Date,i=new Date(t.startTime),a=new Date(t.endTime);return e<i?"pending":e>a?"ended":"active"},onTabChange(t){this.currentTabIndex=t,this.currentBatchStatus=this.batchStatusTabs[t].value},formatDateRange(t,e){const i=new Date(t),a=new Date(e),s=t=>`${(t.getMonth()+1).toString().padStart(2,"0")}-${t.getDate().toString().padStart(2,"0")} ${t.getHours().toString().padStart(2,"0")}:${t.getMinutes().toString().padStart(2,"0")}`;return`${s(i)} ~ ${s(a)}`},getBatchStatusText:t=>({pending:"未开始",active:"进行中",ended:"已结束",paused:"已暂停"}[t.status]||"未知"),getBatchStatusType(t){const e=new Date,i=new Date(t.endTime),a=new Date(t.startTime);return e>i?"error":e<a||"paused"===t.status?"warning":"success"},viewBatchDetail(t){if(t&&"click"===t.type)return void console.log("检测到重复的点击事件，忽略");console.log("viewBatchDetail 接收到的 item:",t),console.log("item.originalData:",t.originalData),console.log("item.id:",t.id);const e=t.originalData||t;console.log("获取到的 batch:",e),console.log("batch.id:",e.id);let i=null;if(e&&e.id?i=e.id:t&&t.id&&(i=t.id),console.log("最终使用的 batchId:",i),!i)return console.error("无效的批次数据，无法跳转",{item:t,batch:e,batchId:i}),void P({title:"无效的批次数据",icon:"none"});const a=`/pages/admin/media/batch-detail?id=${i}`;console.log("跳转URL:",a),D({url:a,fail:t=>{console.error("跳转到批次详情页失败:",t),P({title:"页面跳转失败",icon:"none"})}})},formatBatchItem(t){const e=[{icon:"📅",text:this.formatDateRange(t.startTime,t.endTime)},{icon:"#",text:t.batchId},{icon:"👥",text:`${t.participants||0}人参与`},{icon:"💰",text:`¥${t.totalReward||0}`}];let i="",a="";return t.videos&&t.videos.length>0&&t.videos[0].cover&&(i=t.videos[0].cover,t.videos[0].duration&&(a=this.formatDuration(t.videos[0].duration))),{id:t.id,title:t.title,description:t.description,thumbnail:i,duration:a,statusText:this.getBatchStatusText(t),statusType:this.getBatchStatusType(t),metaItems:e,originalData:t}},createNewBatch(){P({title:"请先选择视频创建批次",icon:"none"})},getCurrentStatusLabel(){const t=this.batchStatusTabs[this.currentTabIndex];return"全部"===t.name?"":t.name},onImageLoad(t){console.log("图片加载成功:",t)},onImageError(t){console.log("图片加载失败:",t)}}},[["render",function(t,e,i,u,h,m){f(v("u-tabs"),L);const p=g,b=k("MediaCard"),y=f(v("u-button"),_),x=f(v("u-empty"),B);return a(),s(p,{class:"container"},{default:o((()=>[n(p,{class:"tabs-container"},{default:o((()=>[c("",!0),n(p,{class:"custom-tabs"},{default:o((()=>[(a(!0),T(S,null,w(h.batchStatusTabs,((t,e)=>(a(),s(p,{key:e,class:l(["custom-tab",h.currentTabIndex===e?"active":""]),onClick:t=>m.onTabChange(e)},{default:o((()=>[r(d(t.name),1)])),_:2},1032,["class","onClick"])))),128))])),_:1})])),_:1}),n(p,{class:"media-list-container"},{default:o((()=>[(a(!0),T(S,null,w(m.filteredBatches,((t,e)=>(a(),s(b,{key:e,item:m.formatBatchItem(t),onClick:m.viewBatchDetail},null,8,["item","onClick"])))),128)),0===m.filteredBatches.length?(a(),s(x,{key:0,mode:"list",text:`暂无${m.getCurrentStatusLabel()}批次`,iconSize:"120",textSize:"16",marginTop:"100"},{default:o((()=>[n(y,{type:"primary",text:"创建批次",onClick:m.createNewBatch,size:"normal",shape:"round"},null,8,["onClick"])])),_:1},8,["text"])):c("",!0)])),_:1})])),_:1})}],["__scopeId","data-v-9b35b385"]])},data:()=>({contentType:"videos"}),methods:{switchContentType(t){this.contentType=t}}},[["render",function(t,e,i,d,u,h){const m=g,p=k("videos-list"),f=k("batches-list");return a(),s(m,{class:"container"},{default:o((()=>[n(m,{class:"page-header"},{default:o((()=>[n(m,{class:"main-tabs"},{default:o((()=>[n(m,{class:l(["main-tab","videos"===u.contentType?"active":""]),onClick:e[0]||(e[0]=t=>h.switchContentType("videos"))},{default:o((()=>[r(" 视频 ")])),_:1},8,["class"]),n(m,{class:l(["main-tab","batches"===u.contentType?"active":""]),onClick:e[1]||(e[1]=t=>h.switchContentType("batches"))},{default:o((()=>[r(" 批次 ")])),_:1},8,["class"])])),_:1})])),_:1}),n(m,{class:"content-container"},{default:o((()=>["videos"===u.contentType?(a(),s(p,{key:0})):c("",!0),"batches"===u.contentType?(a(),s(f,{key:1})):c("",!0)])),_:1})])),_:1})}],["__scopeId","data-v-54d25be6"]]);export{F as default};
