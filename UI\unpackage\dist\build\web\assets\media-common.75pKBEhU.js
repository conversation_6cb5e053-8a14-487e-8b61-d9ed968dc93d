import{m as e,p as t,I as s,N as r,H as a,l as i}from"./index-x-DlrgMM.js";import{a as o,b as n,c as u}from"./format.t5pgP9mx.js";import{s as c,b as d,g as m}from"./adminAuthService.BHB8Py5D.js";const l={methods:{formatDuration:e=>o(e),formatDate:e=>n(e),formatSimpleDate:e=>u(e),buildCompleteFileUrl(e){if(!e)return"";if(e.startsWith("http://")||e.startsWith("https://"))return e;const t=m();if(""===t.trim())return e;return`${t.replace("/api","")}/wwwroot${e.startsWith("/")?e:`/${e}`}`},mapVideoStatus:e=>({0:"scheduled",1:"active",2:"expired"}[e]||"scheduled"),getStatusText(e){if(void 0!==e.compressionStatus){return{0:"未压缩",1:"压缩中",2:"已压缩",3:"压缩失败"}[e.compressionStatus]||"未知压缩状态"}return{expired:"已过期",scheduled:"待发布",active:"已上线"}[e.status]||"未知"},getStatusType(e){if(void 0!==e.compressionStatus){return{0:"info",1:"warning",2:"success",3:"error"}[e.compressionStatus]||"info"}return{expired:"error",scheduled:"warning",active:"success"}[e.status]||"default"},getStatusLabel:e=>({all:"全部",active:"已上线",scheduled:"待发布",expired:"已过期"}[e]||"全部"),showLoading(t="加载中..."){e({title:t,mask:!0})},hideLoading(){t()},showSuccess(e){c(e)},showError(e){d(e)},showConfirm:(e,t)=>new Promise((r=>{s({title:e,content:t,success:e=>{r(e.confirm)},fail:()=>{r(!1)}})})),safeNavigateTo(e,t={}){r({url:e,...t,fail:e=>{console.error("页面跳转失败:",e),this.showError("页面跳转失败")}})},safeNavigateBack(){a({fail:e=>{console.error("页面返回失败:",e),i({url:"/pages/admin/media/index"})}})}}};export{l as m};
