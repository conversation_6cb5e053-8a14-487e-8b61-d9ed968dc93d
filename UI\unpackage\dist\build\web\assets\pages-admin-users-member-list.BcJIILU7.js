import{_ as e,N as a,H as t,q as s,M as r,I as i,r as o,a as n,u as l,c as d,w as m,i as c,d as u,e as g,$ as h,Q as p,a0 as f,a1 as y,b as w,a2 as I,a3 as T,a4 as v,a5 as b,o as _,g as S,v as C,h as F,t as M,J as k,j as x,x as U,F as D,k as P,S as A}from"./index-x-DlrgMM.js";import{U as z}from"./UserInfoCard.XsiHxAWe.js";import{T as $}from"./TimeFilter.DWJ3yWJR.js";import{g as B,a as E,r as R,t as N}from"./sysuser.COxbtp6y.js";import{g as L}from"./employee.BSshoz-s.js";import{b as V}from"./video-user.BRKPw8J7.js";import{f as j,a as q,b as K}from"./employee-data-mapper.B3YJtEOo.js";import{a as W,E as O}from"./api-error-handler.BqxWe_6i.js";import{C as Q}from"./adminAuthService.BHB8Py5D.js";import{m as G}from"./media-common.75pKBEhU.js";import"./format.t5pgP9mx.js";const H={user:{1:"active",0:"disabled",2:"dismissed"},employee:{1:"active",0:"disabled",2:"dismissed"},video:{0:"offline",1:"online",2:"failed",3:"compressing"}},J={1:"admin",2:"manager",3:"employee"},Z={admin:"超管",manager:"管理",employee:"员工",user:"用户"};const X=(e,a)=>class{static formatUser(e,a="user"){var t;return e?{id:e.id||e.userId,username:e.username||e.userName||e.nickname,nickname:e.nickname,avatar:e.avatar,phone:e.phone||e.mobile||"",email:e.email||"",type:a,role:J[e.userType]||a,level:e.userType,status:(null==(t=H[a])?void 0:t[e.status])||"active",statusCode:e.status,disabled:0===e.status,dismissed:2===e.status,employeeId:e.employeeId,employeeName:e.employeeName,managerId:e.managerId||e.parentUserId,managerName:e.managerName||e.parentUserName,registerTime:e.registerTime||e.createTime,lastLoginTime:e.lastLoginTime,userCount:e.userCount||e.totalSubordinateUserCount||0,employeeCount:e.employeeCount||e.directSubordinateCount||0,totalViews:this.extractTimeStats(e.statistics,"views"),totalQuizzes:this.extractTimeStats(e.statistics,"quizzes"),totalRewards:this.extractTimeStats(e.statistics,"rewards"),remark:e.remark||"",createTime:e.createTime,updateTime:e.updateTime}:null}static formatVideo(e){return e?{id:e.id,title:e.title,description:e.description,url:e.url||e.videoUrl,coverUrl:e.coverUrl,duration:e.duration,status:H.video[e.status]||"online",statusCode:e.status,viewCount:e.viewCount||0,rewardAmount:e.rewardAmount||0,questions:e.questions,creator:e.creator||e.createdBy,creatorName:e.creatorName,createTime:e.createTime,updateTime:e.updateTime,fileSize:e.fileSize,format:e.format,resolution:e.resolution}:null}static formatBatch(e){return e?{id:e.id,name:e.name,description:e.description,videoId:e.videoId,videoTitle:e.videoTitle,videoDescription:e.videoDescription,videoCoverUrl:e.videoCoverUrl,videoUrl:e.videoUrl,videoDuration:e.videoDuration,currentParticipants:e.currentParticipants||0,rewardAmount:e.rewardAmount||e.redPacketAmount||0,questions:e.questions,startTime:e.startTime,endTime:e.endTime,creatorId:e.creatorId,status:1===e.status?"online":"offline",statusCode:e.status,createTime:e.createTime,updateTime:e.updateTime}:null}static extractTimeStats(e,a){if(!e||!e[a])return{today:0,yesterday:0,thisWeek:0,thisMonth:0};const t=e[a];return{today:t.today||0,yesterday:t.yesterday||0,thisWeek:t.thisWeek||0,thisMonth:t.thisMonth||0}}static formatDate(e,a="date"){if(!e)return"";const t=new Date(e);if(isNaN(t.getTime()))return"";switch(a){case"date":return t.toISOString().split("T")[0];case"datetime":return t.toLocaleString("zh-CN");case"time":return t.toLocaleTimeString("zh-CN");default:return e}}static formatMoney(e,a="¥"){return null==e?"0.00":`${a}${parseFloat(e).toFixed(2)}`}static formatFileSize(e){if(0===e)return"0 B";const a=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,a)).toFixed(2))+" "+["B","KB","MB","GB","TB"][a]}static formatDuration(e){if(!e||e<0)return"00:00";const a=Math.floor(e/3600),t=Math.floor(e%3600/60),s=e%60;return a>0?`${a.toString().padStart(2,"0")}:${t.toString().padStart(2,"0")}:${s.toString().padStart(2,"0")}`:`${t.toString().padStart(2,"0")}:${s.toString().padStart(2,"0")}`}static getStatusText(e,a="user"){return{active:"正常",disabled:"禁用",dismissed:"离职",online:"上线",offline:"下线",failed:"失败",compressing:"压缩中"}[e]||"未知"}static getRoleText(e){return Z[e]||"未知"}static formatList(e,a,...t){return Array.isArray(e)?e.map((e=>a(e,...t))).filter(Boolean):[]}}.formatDate(e,a);const Y=e({mixins:[G],components:{UserInfoCard:z,TimeFilter:$},data(){return{managerId:null,managerType:"",managerInfo:{id:"",username:"加载中...",avatar:"",registerTime:"",lastLoginTime:"",managerId:"",disabled:!1,dismissed:!1,type:""},employees:[],users:[],searchKeyword:"",activeTimeFilter:"today",customDateRange:{startDate:"",endDate:""},currentUserRole:"admin",showDismissedEmployees:!1,showPasswordModal:!1,passwordForm:{newPassword:"",confirmPassword:""},passwordRules:{newPassword:[{required:!0,message:"请输入新密码",trigger:["blur","change"]},{min:6,max:20,message:"密码长度在 6 到 20 个字符",trigger:["blur","change"]},{pattern:/^(?=.*[a-zA-Z])(?=.*\d)/,message:"密码必须包含字母和数字",trigger:["blur","change"]}],confirmPassword:[{required:!0,message:"请确认密码",trigger:["blur","change"]},{validator:(e,a,t)=>{a!==this.passwordForm.newPassword?t(new Error("两次输入的密码不一致")):t()},trigger:["blur","change"]}]},currentUser:null,showManagerActionSheetVisible:!1,loading:!1}},computed:{pageTitle(){return"manager"===this.managerType?`${this.managerInfo.username}的成员`:`${this.managerInfo.username}的用户`},managerActionList(){const e=[{name:this.managerInfo.disabled?"启用账号":"禁用账号",value:"toggleStatus",color:this.managerInfo.disabled?"#52c41a":"#ff4d4f"},{name:"重置密码",value:"resetPassword",color:"#1890ff"}];return"employee"===this.managerType&&(e.push({name:this.managerInfo.dismissed?"恢复在职":"员工离职",value:"toggleDismiss",color:this.managerInfo.dismissed?"#52c41a":"#faad14"}),e.push({name:"用户转移",value:"transferUser",color:"#722ed1"})),e},allEmployees(){if("manager"!==this.managerType||!this.employees.length)return[];let e=this.employees.filter((e=>e.managerId===this.managerId));if(this.searchKeyword){const a=this.searchKeyword.toLowerCase();e=e.filter((e=>e.username.toLowerCase().includes(a)||e.phone&&e.phone.includes(a)))}return e},activeEmployees(){return this.allEmployees.filter((e=>!e.dismissed))},dismissedEmployees(){return this.allEmployees.filter((e=>e.dismissed))},filteredUsers(){let e=this.users;if("manager"===this.managerType){const a=this.employees.filter((e=>e.managerId===this.managerId)).map((e=>e.id));e=e.filter((e=>a.includes(e.employeeId)))}else"employee"===this.managerType&&(e=e.filter((e=>e.employeeId===this.managerId)));if(!this.searchKeyword)return e;const a=this.searchKeyword.toLowerCase();return e.filter((e=>e.username.toLowerCase().includes(a)||e.phone&&e.phone.includes(a)))}},async onLoad(e){e.id&&(this.managerId=e.id,this.managerType=e.type||"employee"),await this.loadManagerInfo(),await this.loadSubordinates(),this.currentUserRole=this.managerType},methods:{async loadManagerInfo(){if(this.managerId)try{let a;if("manager"===this.managerType)a=await W((()=>B(this.managerId)),{...O.important,loadingTitle:"加载管理者信息...",errorTitle:"加载失败"}),a.success&&a.data&&(this.managerInfo=j(a.data),this.managerInfo.type=this.managerType);else try{a=await W((()=>B(this.managerId)),{...O.silent}),a.success&&a.data&&(this.managerInfo=j(a.data),this.managerInfo.type=this.managerType)}catch(e){console.log("SysUser API 失败，尝试 Employee API"),a=await W((()=>L(this.managerId)),{...O.important,loadingTitle:"加载员工信息...",errorTitle:"加载失败"}),a.success&&a.data&&(this.managerInfo=q(a.data),this.managerInfo.type=this.managerType)}}catch(a){console.error("加载管理者信息失败:",a),this.managerInfo={id:this.managerId||"",username:"加载失败",avatar:"",registerTime:"",lastLoginTime:"",managerId:"",disabled:!1,dismissed:!1,type:this.managerType}}},async loadSubordinates(){try{if(!this.managerId)return;"manager"===this.managerType?await this.loadEmployees():await this.loadUsers()}catch(e){console.error("加载下级成员失败:",e),this.employees=[],this.users=[]}},async loadEmployees(){try{const e=await W((()=>E(this.managerId,{pageIndex:1,pageSize:100})),{...O.silent});e.success&&e.data&&(this.employees=e.data.map((e=>j(e))))}catch(e){console.error("加载员工列表失败:",e)}},async loadUsers(){try{const e=await W((()=>V(this.managerId,{PageIndex:1,PageSize:100})),{...O.silent});e.success&&e.data&&(this.users=e.data.items.map((e=>K(e))))}catch(e){console.error("加载用户列表失败:",e)}},handleTimeFilterChange(e){this.activeTimeFilter=e,this.customDateRange={startDate:e.startDate,endDate:e.endDate},console.log("时间筛选变化:",e)},formattedUserInfo(e){let a="未知员工";if(e.employeeId)if("employee"===this.managerType&&e.employeeId===this.managerId)a=this.managerInfo.username||"当前员工";else{const t=this.employees.find((a=>a.id===e.employeeId));a=t?t.username:`员工${e.employeeId}`}return{...e,type:"user",employeeName:a,disabled:e.disabled||0===e.status||!1}},formattedEmployeeInfo(e){let a="未知管理";return e.managerId&&(a="manager"===this.managerType&&e.managerId===this.managerId?this.managerInfo.username||"当前管理":`管理${e.managerId}`),{...e,type:"employee",managerName:a,disabled:e.disabled||0===e.status||!1}},viewUserDetail(e){a({url:`/pages/admin/users/info?userId=${e.id}`})},viewEmployeeDetail(e){a({url:`/pages/admin/users/member-list?id=${e.id}&type=employee`})},goBack(){t()},handleChangePassword(e){console.log("修改密码:",e),this.currentUser=e,this.showPasswordModal=!0},closePasswordModal(){this.showPasswordModal=!1,this.passwordForm={newPassword:"",confirmPassword:""},this.currentUser=null,this.$nextTick((()=>{this.$refs.passwordForm&&this.$refs.passwordForm.resetFields()}))},async confirmResetPassword(){try{if(!(await this.$refs.passwordForm.validate()))return}catch(e){return}try{this.loading=!0;const e=Q.MD5(this.passwordForm.newPassword).toString();await R({userId:this.currentUser.id,newPassword:e}),s({title:"密码重置成功",icon:"success"}),this.closePasswordModal()}catch(e){s({title:e.message||"密码重置失败",icon:"none"})}finally{this.loading=!1}},copyManagerId(){this.managerInfo.id&&r({data:this.managerInfo.id.toString(),success:()=>{s({title:"ID已复制",icon:"success"})}})},getManagerName:e=>e?"未知管理":"未分配",formatDate:e=>X(e,"datetime"),showManagerActionSheet(){this.showManagerActionSheetVisible=!0},closeManagerActionSheet(){this.showManagerActionSheetVisible=!1},handleManagerAction(e){switch(this.closeManagerActionSheet(),e.value){case"toggleStatus":this.handleManagerToggleStatus();break;case"resetPassword":this.handleManagerResetPassword();break;case"toggleDismiss":this.handleEmployeeDismiss();break;case"transferUser":this.handleUserTransfer()}},async handleManagerToggleStatus(){this.closeManagerActionSheet();const e=this.managerInfo.disabled?"启用":"禁用",a=this.managerInfo.disabled?1:0,t="manager"===this.managerType?"管理":"员工";i({title:`确认${e}`,content:`确定要${e}${t} ${this.managerInfo.username} 的账号吗？`,success:async t=>{if(t.confirm)try{await N(this.managerInfo.id,a),this.managerInfo.disabled=!this.managerInfo.disabled,s({title:`账号已${e}`,icon:"success"})}catch(r){s({title:r.message||`${e}失败`,icon:"none"})}}})},handleManagerResetPassword(){this.closeManagerActionSheet(),this.currentUser=this.managerInfo,this.showPasswordModal=!0},handleEmployeeDismiss(){this.closeManagerActionSheet(),this.managerInfo.dismissed?i({title:"确认恢复",content:`确定要恢复员工 ${this.managerInfo.username} 的在职状态吗？`,success:e=>{if(e.confirm){this.managerInfo.dismissed=!1;const e=this.employees.findIndex((e=>e.id===this.managerId));-1!==e&&(this.employees[e].dismissed=!1),s({title:"员工已恢复在职",icon:"success"})}}}):i({title:"确认离职",content:`确定要将员工 ${this.managerInfo.username} 设为离职状态吗？`,success:e=>{e.confirm&&(this.managerInfo.dismissed=!0,s({title:"员工已设为离职",icon:"success"}))}})},handleUserTransfer(){this.closeManagerActionSheet(),s({title:"用户转移功能开发中",icon:"none"})}}},[["render",function(e,a,t,s,r,i){const z=c,$=P,B=o(n("u-icon"),u),E=o(n("u-button"),g),R=o(n("u-alert"),h),N=l("TimeFilter"),L=l("UserInfoCard"),V=o(n("u-tag"),p),j=o(n("u-collapse-item"),f),q=o(n("u-collapse"),y),K=o(n("u-input"),w),W=o(n("u-form-item"),I),O=o(n("u-form"),T),Q=o(n("u-modal"),v),G=o(n("u-action-sheet"),b),H=A;return _(),d(z,{class:"container"},{default:m((()=>[S(z,{class:"manager-card fixed-header"},{default:m((()=>[S(z,{class:"custom-card"},{default:m((()=>[S(z,{class:"manager-header"},{default:m((()=>[S(z,{class:"manager-avatar-section"},{default:m((()=>[r.managerInfo.avatar?(_(),C("img",{key:0,src:e.buildCompleteFileUrl(r.managerInfo.avatar),style:{width:"60px",height:"60px","border-radius":"8px","object-fit":"cover"}},null,8,["src"])):(_(),d(z,{key:1,class:"avatar-placeholder",style:{width:"60px",height:"60px","border-radius":"8px","background-color":"#f0f9ff",color:"#186BFF",display:"flex","align-items":"center","justify-content":"center","font-size":"24px","font-weight":"bold"}},{default:m((()=>{var e,a;return[F(M((null==(a=null==(e=r.managerInfo.username)?void 0:e.charAt(0))?void 0:a.toUpperCase())||"U"),1)]})),_:1}))])),_:1}),S(z,{class:"manager-basic-info"},{default:m((()=>[S(z,{class:"manager-name-row"},{default:m((()=>[S($,{class:"manager-name"},{default:m((()=>[F(M(r.managerInfo.username),1)])),_:1}),S(E,{type:"primary",size:"mini",shape:"circle",onClick:k(i.copyManagerId,["stop"]),customStyle:{marginLeft:"12rpx",minWidth:"auto",height:"48rpx",padding:"0 16rpx"}},{default:m((()=>[S(B,{name:"copy",size:"12",color:"#fff"})])),_:1},8,["onClick"])])),_:1}),S(z,{class:"manager-actions"},{default:m((()=>[S(E,{type:"primary",size:"small",shape:"round",onClick:k(i.showManagerActionSheet,["stop"]),text:"管理操作",customStyle:{marginTop:"16rpx"}},null,8,["onClick"])])),_:1})])),_:1})])),_:1}),S(z,{class:"manager-details"},{default:m((()=>[S(z,{class:"detail-grid"},{default:m((()=>[S(z,{class:"detail-item"},{default:m((()=>[S(B,{name:"calendar",size:"20",color:"#186BFF"}),S(z,{class:"detail-content"},{default:m((()=>[S($,{class:"detail-label"},{default:m((()=>[F("注册时间")])),_:1}),S($,{class:"detail-value"},{default:m((()=>[F(M(i.formatDate(r.managerInfo.registerTime)),1)])),_:1})])),_:1})])),_:1}),S(z,{class:"detail-item"},{default:m((()=>[S(B,{name:"clock",size:"20",color:"#186BFF"}),S(z,{class:"detail-content"},{default:m((()=>[S($,{class:"detail-label"},{default:m((()=>[F("最后登录")])),_:1}),S($,{class:"detail-value"},{default:m((()=>[F(M(i.formatDate(r.managerInfo.lastLoginTime)||"从未登录"),1)])),_:1})])),_:1})])),_:1}),"manager"===r.managerType?(_(),d(z,{key:0,class:"detail-item"},{default:m((()=>[S(B,{name:"account",size:"20",color:"#186BFF"}),S(z,{class:"detail-content"},{default:m((()=>[S($,{class:"detail-label"},{default:m((()=>[F("管理员工")])),_:1}),S($,{class:"detail-value"},{default:m((()=>[F(M(i.activeEmployees.length)+"人",1)])),_:1})])),_:1})])),_:1})):x("",!0),"manager"===r.managerType?(_(),d(z,{key:1,class:"detail-item"},{default:m((()=>[S(B,{name:"account-fill",size:"20",color:"#186BFF"}),S(z,{class:"detail-content"},{default:m((()=>[S($,{class:"detail-label"},{default:m((()=>[F("总用户数")])),_:1}),S($,{class:"detail-value"},{default:m((()=>[F(M(i.filteredUsers.length)+"人",1)])),_:1})])),_:1})])),_:1})):x("",!0),"employee"===r.managerType?(_(),d(z,{key:2,class:"detail-item"},{default:m((()=>[S(B,{name:"level",size:"20",color:"#186BFF"}),S(z,{class:"detail-content"},{default:m((()=>[S($,{class:"detail-label"},{default:m((()=>[F("推广用户")])),_:1}),S($,{class:"detail-value"},{default:m((()=>[F(M(i.filteredUsers.length)+"人",1)])),_:1})])),_:1})])),_:1})):x("",!0),"employee"===r.managerType?(_(),d(z,{key:3,class:"detail-item"},{default:m((()=>[S(B,{name:"man",size:"20",color:"#186BFF"}),S(z,{class:"detail-content"},{default:m((()=>[S($,{class:"detail-label"},{default:m((()=>[F("所属管理")])),_:1}),S($,{class:"detail-value"},{default:m((()=>[F(M(i.getManagerName(r.managerInfo.managerId)),1)])),_:1})])),_:1})])),_:1})):x("",!0)])),_:1})])),_:1})])),_:1})])),_:1}),"employee"===r.managerType&&r.managerInfo&&r.managerInfo.dismissed&&i.filteredUsers.length>0?(_(),d(R,{key:0,type:"warning","show-icon":!0,closable:!1,title:"该员工已离职，建议将用户转移给其他员工",description:`当前有 ${i.filteredUsers.length} 个用户需要转移`,customStyle:{margin:"20rpx"}},{icon:m((()=>[S(B,{name:"error-circle",size:"20",color:"#faad14"})])),desc:m((()=>[S(z,{style:{"margin-top":"16rpx"}},{default:m((()=>[S(E,{type:"warning",size:"small",shape:"round",onClick:e.showTransferModal,text:"立即转移",customStyle:{marginTop:"16rpx"}},{default:m((()=>[S(B,{name:"reload",size:"14",color:"#fff",style:{"margin-right":"8rpx"}})])),_:1},8,["onClick"])])),_:1})])),_:1},8,["description"])):x("",!0),S(H,{class:"scrollable-content","scroll-y":"true"},{default:m((()=>[S(N,{modelValue:r.activeTimeFilter,"onUpdate:modelValue":a[0]||(a[0]=e=>r.activeTimeFilter=e),onChange:i.handleTimeFilterChange},null,8,["modelValue","onChange"]),"manager"===r.managerType&&i.activeEmployees.length>0?(_(),d(z,{key:0,class:"member-list"},{default:m((()=>[S(z,{class:"member-cards-container"},{default:m((()=>[(_(!0),C(D,null,U(i.activeEmployees,(e=>(_(),d(z,{class:"member-card",key:e.id,onClick:a=>i.viewEmployeeDetail(e)},{default:m((()=>[S(L,{userInfo:i.formattedEmployeeInfo(e),timeFilter:r.activeTimeFilter,customDateRange:r.customDateRange,showDetailBtn:!1,showFooterBtns:!1},null,8,["userInfo","timeFilter","customDateRange"])])),_:2},1032,["onClick"])))),128))])),_:1}),i.dismissedEmployees.length>0?(_(),d(q,{key:0,customStyle:{margin:"20rpx 0"}},{default:m((()=>[S(j,{title:`已离职员工 (${i.dismissedEmployees.length})`,name:"dismissed",icon:{name:"account",color:"#909399"}},{default:m((()=>[S(z,{class:"dismissed-content"},{default:m((()=>[(_(!0),C(D,null,U(i.dismissedEmployees,(e=>(_(),d(z,{class:"member-card dismissed-card",key:e.id,onClick:a=>i.viewEmployeeDetail(e)},{default:m((()=>[S(L,{userInfo:i.formattedEmployeeInfo(e),timeFilter:r.activeTimeFilter,customDateRange:r.customDateRange,showDetailBtn:!1,showFooterBtns:!1},null,8,["userInfo","timeFilter","customDateRange"]),S(V,{text:"已离职",type:"warning",shape:"circle",size:"mini",customStyle:{position:"absolute",top:"16rpx",right:"16rpx",zIndex:10}},{icon:m((()=>[S(B,{name:"error-circle",size:"12",color:"#faad14"})])),_:1})])),_:2},1032,["onClick"])))),128))])),_:1})])),_:1},8,["title"])])),_:1})):x("",!0)])),_:1})):x("",!0),"employee"===r.managerType?(_(),d(z,{key:1,class:"member-list"},{default:m((()=>[S(z,{class:"member-cards-container"},{default:m((()=>[(_(!0),C(D,null,U(i.filteredUsers,(e=>(_(),d(z,{class:"member-card",key:e.id,onClick:a=>i.viewUserDetail(e)},{default:m((()=>[S(L,{userInfo:i.formattedUserInfo(e),showDetailBtn:!1,showFooterBtns:!1},null,8,["userInfo"])])),_:2},1032,["onClick"])))),128))])),_:1})])),_:1})):x("",!0),S(Q,{show:r.showPasswordModal,"onUpdate:show":a[3]||(a[3]=e=>r.showPasswordModal=e),title:"重置密码",closeOnClickOverlay:!0,onClose:i.closePasswordModal,customStyle:{maxWidth:"600rpx"},showCancelButton:!1,showConfirmButton:!1},{default:m((()=>[S(z,{class:"modal-form"},{default:m((()=>[S(R,{type:"warning","show-icon":!0,closable:!1,title:"重置密码将覆盖用户当前密码，请谨慎操作",customStyle:{marginBottom:"32rpx"}}),S(O,{model:r.passwordForm,rules:r.passwordRules,ref:"passwordForm",labelWidth:"120",labelPosition:"top"},{default:m((()=>[S(W,{label:"新密码",prop:"newPassword",required:!0},{default:m((()=>[S(K,{modelValue:r.passwordForm.newPassword,"onUpdate:modelValue":a[1]||(a[1]=e=>r.passwordForm.newPassword=e),placeholder:"请输入新密码",type:"password",border:"surround",clearable:"",customStyle:{width:"100%"}},null,8,["modelValue"])])),_:1}),S(W,{label:"确认密码",prop:"confirmPassword",required:!0},{default:m((()=>[S(K,{modelValue:r.passwordForm.confirmPassword,"onUpdate:modelValue":a[2]||(a[2]=e=>r.passwordForm.confirmPassword=e),placeholder:"请再次输入新密码",type:"password",border:"surround",clearable:"",customStyle:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"]),S(z,{class:"password-tips"},{default:m((()=>[S($,{class:"tip-title"},{default:m((()=>[F("密码要求：")])),_:1}),S($,{class:"tip-item"},{default:m((()=>[F("• 长度不少于6位")])),_:1}),S($,{class:"tip-item"},{default:m((()=>[F("• 必须包含字母和数字")])),_:1}),S($,{class:"tip-item"},{default:m((()=>[F("• 建议包含特殊字符")])),_:1})])),_:1}),S(z,{class:"modal-buttons"},{default:m((()=>[S(E,{type:"info",onClick:i.closePasswordModal,text:"取消",size:"large",customStyle:{width:"45%",marginRight:"10%"}},null,8,["onClick"]),S(E,{type:"primary",onClick:i.confirmResetPassword,loading:r.loading,text:"确认重置",size:"large",customStyle:{width:"45%"}},null,8,["onClick","loading"])])),_:1})])),_:1})])),_:1},8,["show","onClose"]),S(G,{show:r.showManagerActionSheetVisible,"onUpdate:show":a[4]||(a[4]=e=>r.showManagerActionSheetVisible=e),actions:i.managerActionList,title:"管理操作",onSelect:i.handleManagerAction,onClose:i.closeManagerActionSheet,closeOnClickOverlay:!0,safeAreaInsetBottom:!0},null,8,["show","actions","onSelect","onClose"])])),_:1})])),_:1})}],["__scopeId","data-v-cd4af56c"]]);export{Y as default};
