import{m as o,p as r}from"./index-x-DlrgMM.js";import{b as t}from"./adminAuthService.BHB8Py5D.js";async function i(i,a={}){const{fallback:s=null,loadingTitle:n="加载中...",errorTitle:e="加载失败",showLoading:c=!0,showError:h=!0}=a;try{c&&o({title:n});const t=await i();if(c&&r(),t&&t.success)return t;throw new Error((null==t?void 0:t.msg)||"API调用失败")}catch(l){if(c&&r(),h&&t(e),s&&"function"==typeof s)try{return await s()}catch(w){throw w}throw l}}const a={silent:{showLoading:!1,showError:!1},quick:{loadingTitle:"加载中...",showLoading:!0,showError:!0},important:{loadingTitle:"处理中，请稍候...",errorTitle:"操作失败，请重试",showLoading:!0,showError:!0},background:{showLoading:!1,showError:!1,retry:!0,maxRetries:2}};export{a as E,i as a};
