import{_ as t,q as e,m as a,p as i,H as s,c as l,w as r,i as n,o as d,g as o,h as m,t as c,aq as h,U as u,ae as f,y as g}from"./index-x-DlrgMM.js";import{c as T}from"./batch.B7qFZ3Nu.js";import{m as p}from"./media-common.75pKBEhU.js";import"./adminAuthService.BHB8Py5D.js";import"./format.t5pgP9mx.js";const D=t({mixins:[p],data:()=>({videoId:0,batchTitle:"",batchDescription:"",startDate:"",startTime:"",endDate:"",endTime:""}),computed:{isFormValid(){return this.batchTitle&&this.batchTitle.trim()&&this.batchTitle.trim().length>=2}},onLoad(t){if(console.log("创建批次页接收到的参数:",t),t&&(t.videoId||t.id)){const e=parseInt(t.videoId||t.id);this.videoId=isNaN(e)?0:e}this.initDateTimeValues()},methods:{initDateTimeValues(){const t=new Date;this.startDate=this.formatDate(t),this.startTime=this.formatTime(t);const e=new Date;e.setDate(e.getDate()+30),this.endDate=this.formatDate(e),this.endTime=this.formatTime(e)},formatDate:t=>`${t.getFullYear()}-${String(t.getMonth()+1).padStart(2,"0")}-${String(t.getDate()).padStart(2,"0")}`,formatTime:t=>`${String(t.getHours()).padStart(2,"0")}:${String(t.getMinutes()).padStart(2,"0")}`,onStartDateChange(t){this.startDate=t.detail.value},onStartTimeChange(t){this.startTime=t.detail.value},onEndDateChange(t){this.endDate=t.detail.value},onEndTimeChange(t){this.endTime=t.detail.value},getStartDateTime(){if(!this.startDate||!this.startTime)return"";let t=this.startDate;return t.includes(" ")&&(t=t.split(" ")[0]),`${t}T${this.startTime}:00`},getEndDateTime(){if(!this.endDate||!this.endTime)return"";let t=this.endDate;return t.includes(" ")&&(t=t.split(" ")[0]),`${t}T${this.endTime}:00`},async createBatch(){try{const t=this.validateForm();if(!t.valid)return void e({title:t.message,icon:"none"});a({title:"创建批次中..."});const s={name:this.batchTitle.trim(),description:this.batchDescription.trim()||"暂无描述",videoId:this.videoId,startTime:this.getStartDateTime(),endTime:this.getEndDateTime(),redPacketAmount:0},l=await T(s);if(!l.success)throw new Error(l.msg||"创建批次失败");i(),e({title:"批次创建成功",icon:"success"}),setTimeout((()=>{this.goBack()}),1500)}catch(t){console.error("创建批次失败:",t),i(),e({title:t.message||"创建失败",icon:"none"})}},validateForm(){if(!this.batchTitle||""===this.batchTitle.trim())return{valid:!1,message:"请输入批次标题"};if(this.batchTitle.trim().length<2)return{valid:!1,message:"批次标题至少需要2个字符"};if(this.batchTitle.trim().length>50)return{valid:!1,message:"批次标题不能超过50个字符"};if(this.batchDescription&&this.batchDescription.length>200)return{valid:!1,message:"批次描述不能超过200个字符"};if(!this.startDate||!this.startTime)return{valid:!1,message:"请选择活动开始时间"};if(!this.endDate||!this.endTime)return{valid:!1,message:"请选择活动结束时间"};if(!this.videoId||this.videoId<=0)return{valid:!1,message:"视频ID无效，请重新进入页面"};return new Date(this.getStartDateTime())>=new Date(this.getEndDateTime())?{valid:!1,message:"开始时间必须早于结束时间"}:{valid:!0,message:""}},goBack(){s()}}},[["render",function(t,e,a,i,s,T){const p=n,D=u,v=f,b=g;return d(),l(p,{class:"container"},{default:r((()=>[o(p,{class:"publish-form"},{default:r((()=>[o(p,{class:"form-section"},{default:r((()=>[o(p,{class:"section-title"},{default:r((()=>[m("批次基本信息")])),_:1}),o(p,{class:"form-group"},{default:r((()=>[o(p,{class:"form-label required"},{default:r((()=>[m("批次标题")])),_:1}),o(D,{type:"text",class:"form-input",modelValue:s.batchTitle,"onUpdate:modelValue":e[0]||(e[0]=t=>s.batchTitle=t),placeholder:"请输入批次标题（2-50字符）",maxlength:"50"},null,8,["modelValue"]),o(p,{class:"form-hint"},{default:r((()=>[m(c(s.batchTitle.length)+"/50",1)])),_:1})])),_:1}),o(p,{class:"form-group"},{default:r((()=>[o(p,{class:"form-label required"},{default:r((()=>[m("活动开始日期")])),_:1}),o(v,{mode:"date",value:s.startDate,onChange:T.onStartDateChange},{default:r((()=>[o(p,{class:"form-input picker-display"},{default:r((()=>[m(c(s.startDate||"请选择开始日期"),1)])),_:1})])),_:1},8,["value","onChange"])])),_:1}),o(p,{class:"form-group"},{default:r((()=>[o(p,{class:"form-label required"},{default:r((()=>[m("活动开始时间")])),_:1}),o(v,{mode:"time",value:s.startTime,onChange:T.onStartTimeChange},{default:r((()=>[o(p,{class:"form-input picker-display"},{default:r((()=>[m(c(s.startTime||"请选择开始时间"),1)])),_:1})])),_:1},8,["value","onChange"])])),_:1}),o(p,{class:"form-group"},{default:r((()=>[o(p,{class:"form-label required"},{default:r((()=>[m("活动结束日期")])),_:1}),o(v,{mode:"date",value:s.endDate,onChange:T.onEndDateChange},{default:r((()=>[o(p,{class:"form-input picker-display"},{default:r((()=>[m(c(s.endDate||"请选择结束日期"),1)])),_:1})])),_:1},8,["value","onChange"])])),_:1}),o(p,{class:"form-group"},{default:r((()=>[o(p,{class:"form-label required"},{default:r((()=>[m("活动结束时间")])),_:1}),o(v,{mode:"time",value:s.endTime,onChange:T.onEndTimeChange},{default:r((()=>[o(p,{class:"form-input picker-display"},{default:r((()=>[m(c(s.endTime||"请选择结束时间"),1)])),_:1})])),_:1},8,["value","onChange"])])),_:1}),o(p,{class:"action-buttons"},{default:r((()=>[o(b,{class:"action-btn cancel-btn",onClick:T.goBack},{default:r((()=>[h("i",{class:"fa fa-times"}),m(" 取消 ")])),_:1},8,["onClick"]),o(b,{class:"action-btn publish-btn",onClick:e[1]||(e[1]=t=>T.createBatch()),disabled:!T.isFormValid},{default:r((()=>[h("i",{class:"fa fa-check"}),m(" 创建批次 ")])),_:1},8,["disabled"])])),_:1})])),_:1})])),_:1})])),_:1})}],["__scopeId","data-v-ca7122ff"]]);export{D as default};
