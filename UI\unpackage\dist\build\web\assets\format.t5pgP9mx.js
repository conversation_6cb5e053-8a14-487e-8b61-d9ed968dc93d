function t(t){if(!t||t<0)return"00:00";const r=Math.floor(t/60),n=Math.floor(t%60);return`${r.toString().padStart(2,"0")}:${n.toString().padStart(2,"0")}`}function r(t){if(!t)return"";try{const r=new Date(t);if(isNaN(r.getTime()))return"";const n=r.getFullYear(),e=(r.getMonth()+1).toString().padStart(2,"0");return`${n}-${e}-${r.getDate().toString().padStart(2,"0")}`}catch(r){return console.error("日期格式化失败:",r),""}}function n(t){if(!t)return"未设置";try{const r=new Date(t);if(isNaN(r.getTime()))return"未设置";const n=r.getFullYear(),e=(r.getMonth()+1).toString().padStart(2,"0"),a=r.getDate().toString().padStart(2,"0"),o=r.getHours().toString().padStart(2,"0");return`${n}-${e}-${a} ${o}:${r.getMinutes().toString().padStart(2,"0")}`}catch(r){return console.error("日期时间格式化失败:",r),"未设置"}}function e(t){if(!t||0===t)return"0 B";const r=Math.floor(Math.log(t)/Math.log(1024));return parseFloat((t/Math.pow(1024,r)).toFixed(2))+" "+["B","KB","MB","GB","TB"][r]}export{t as a,n as b,r as c,e as f};
