# 应用配置说明

## 概述
此目录包含应用的外部配置文件，这些文件可以在项目发布后直接修改，无需重新编译和发布。

## 配置文件

### app-config.js
主要的应用配置文件，包含以下配置项：

#### UIProjectUrl
- **说明**: 项目前端访问地址
- **默认值**: `http://localhost:5173`
- **示例**:
  - 开发环境: `http://localhost:5173`
  - 生产环境: `https://your-domain.com` 或 `http://your-server-ip:port`

#### 其他配置项
- `apiTimeout`: API请求超时时间（毫秒）
- `debugMode`: 是否启用调试模式
- `version`: 应用版本号

## 如何修改配置

### 开发环境
1. 直接修改 `UI/static/config/app-config.js` 文件
2. 保存后刷新浏览器页面即可生效

### 生产环境
1. 找到服务器上的配置文件路径：`/path/to/your/project/static/config/app-config.js`
2. 使用文本编辑器修改配置文件
3. 保存文件
4. 用户刷新页面后新配置即可生效

## 注意事项

1. **语法正确性**: 修改配置时请确保JavaScript语法正确，否则可能导致应用无法正常运行
2. **备份**: 修改前建议备份原配置文件
3. **测试**: 修改后请测试相关功能是否正常
4. **缓存**: 如果修改后没有生效，请清除浏览器缓存或强制刷新页面

## 配置示例

```javascript
window.APP_CONFIG = {
  // 生产环境配置示例
  UIProjectUrl: 'https://your-domain.com',
  apiTimeout: 30000,
  debugMode: false,
  version: '1.0.0'
};
```

## 故障排除

如果修改配置后出现问题：

1. 检查JavaScript语法是否正确
2. 查看浏览器控制台是否有错误信息
3. 恢复备份的配置文件
4. 联系技术支持
